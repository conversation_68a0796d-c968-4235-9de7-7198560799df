import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  ActivityIndicator,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

export type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost';
export type ButtonSize = 'small' | 'medium' | 'large';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: ButtonVariant;
  size?: ButtonSize;
  disabled?: boolean;
  loading?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  fullWidth?: boolean;
}

export const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  style,
  textStyle,
  fullWidth = false,
}) => {
  const { theme } = useTheme();

  const getButtonStyle = (): ViewStyle => {
    const baseStyle = theme.components.button[variant];
    const sizeStyle = getSizeStyle();
    
    return {
      ...baseStyle,
      ...sizeStyle,
      opacity: disabled ? 0.6 : 1,
      width: fullWidth ? '100%' : 'auto',
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'row',
    };
  };

  const getSizeStyle = (): ViewStyle => {
    switch (size) {
      case 'small':
        return {
          paddingHorizontal: theme.spacing.lg,
          paddingVertical: theme.spacing.sm,
          minHeight: 36,
        };
      case 'large':
        return {
          paddingHorizontal: theme.spacing['3xl'],
          paddingVertical: theme.spacing.lg,
          minHeight: 52,
        };
      default: // medium
        return {
          paddingHorizontal: theme.spacing['2xl'],
          paddingVertical: theme.spacing.md,
          minHeight: 44,
        };
    }
  };

  const getTextStyle = (): TextStyle => {
    const baseTextStyle = theme.components.button[variant];
    const sizeTextStyle = getTextSizeStyle();
    
    return {
      color: baseTextStyle.color,
      fontSize: sizeTextStyle.fontSize,
      fontWeight: baseTextStyle.fontWeight as any,
      textAlign: 'center',
    };
  };

  const getTextSizeStyle = () => {
    switch (size) {
      case 'small':
        return { fontSize: theme.typography.fontSize.sm };
      case 'large':
        return { fontSize: theme.typography.fontSize.lg };
      default:
        return { fontSize: theme.typography.fontSize.base };
    }
  };

  return (
    <TouchableOpacity
      style={[getButtonStyle(), style]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
    >
      {loading && (
        <ActivityIndicator
          size="small"
          color={getTextStyle().color}
          style={{ marginRight: theme.spacing.sm }}
        />
      )}
      <Text style={[getTextStyle(), textStyle]}>
        {title}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  // Additional styles can be added here if needed
});
