import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React from 'react';
import {
    <PERSON><PERSON>,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { <PERSON><PERSON>, Card, Header } from '../../components/ui';
import { Strings } from '../../constants';
import { useTheme } from '../../contexts/ThemeContext';

export default function Profile() {
  const { theme, toggleTheme, themeMode } = useTheme();

  const userInfo = {
    name: 'أحمد محمد',
    email: '<EMAIL>',
    phone: '+212 6 12 34 56 78',
    userType: 'seeker', // or 'provider'
    rating: 4.8,
    completedOrders: 24,
    memberSince: '2023',
  };

  const menuItems = [
    {
      id: 1,
      title: Strings.profile.editProfile,
      icon: 'person-outline',
      onPress: () => console.log('Edit profile'),
      showArrow: true,
    },
    {
      id: 2,
      title: Strings.profile.myOrders,
      icon: 'receipt-outline',
      onPress: () => console.log('My orders'),
      showArrow: true,
    },
    {
      id: 3,
      title: Strings.profile.favorites,
      icon: 'heart-outline',
      onPress: () => console.log('Favorites'),
      showArrow: true,
    },
    {
      id: 4,
      title: Strings.profile.wallet,
      icon: 'wallet-outline',
      onPress: () => console.log('Wallet'),
      showArrow: true,
    },
    {
      id: 5,
      title: Strings.profile.settings,
      icon: 'settings-outline',
      onPress: () => console.log('Settings'),
      showArrow: true,
    },
    {
      id: 6,
      title: Strings.profile.support,
      icon: 'help-circle-outline',
      onPress: () => console.log('Support'),
      showArrow: true,
    },
    {
      id: 7,
      title: Strings.profile.about,
      icon: 'information-circle-outline',
      onPress: () => console.log('About'),
      showArrow: true,
    },
  ];

  const handleLogout = () => {
    Alert.alert(
      'تسجيل الخروج',
      'هل أنت متأكد من أنك تريد تسجيل الخروج؟',
      [
        {
          text: 'إلغاء',
          style: 'cancel',
        },
        {
          text: 'تسجيل الخروج',
          style: 'destructive',
          onPress: () => {
            // TODO: Implement logout logic
            router.replace('/welcome');
          },
        },
      ]
    );
  };

  const getThemeText = () => {
    switch (themeMode) {
      case 'light':
        return Strings.theme.light;
      case 'dark':
        return Strings.theme.dark;
      case 'system':
        return Strings.theme.system;
      default:
        return Strings.theme.system;
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollContainer: {
      padding: theme.spacing.screen.horizontal,
      paddingBottom: theme.spacing['4xl'],
    },
    profileSection: {
      alignItems: 'center',
      marginBottom: theme.spacing['2xl'],
    },
    avatarContainer: {
      width: 100,
      height: 100,
      borderRadius: 50,
      backgroundColor: theme.colors.primary,
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: theme.spacing.lg,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.2,
      shadowRadius: 8,
      elevation: 4,
    },
    avatarText: {
      ...theme.typography.textStyles.h1,
      color: theme.colors.textOnPrimary,
      fontWeight: '700',
    },
    userName: {
      ...theme.typography.textStyles.h2,
      color: theme.colors.text,
      textAlign: 'center',
      marginBottom: theme.spacing.xs,
    },
    userEmail: {
      ...theme.typography.textStyles.body,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      marginBottom: theme.spacing.sm,
    },
    userType: {
      ...theme.typography.textStyles.bodySmall,
      color: theme.colors.primary,
      fontWeight: '600',
      textAlign: 'center',
    },
    statsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      marginTop: theme.spacing.lg,
      paddingTop: theme.spacing.lg,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
    },
    statItem: {
      alignItems: 'center',
    },
    statValue: {
      ...theme.typography.textStyles.h3,
      color: theme.colors.text,
      fontWeight: '700',
      marginBottom: theme.spacing.xs,
    },
    statLabel: {
      ...theme.typography.textStyles.caption,
      color: theme.colors.textSecondary,
      textAlign: 'center',
    },
    menuSection: {
      marginBottom: theme.spacing.lg,
    },
    menuItem: {
      flexDirection: theme.isRTL ? 'row-reverse' : 'row',
      alignItems: 'center',
      padding: theme.spacing.lg,
      marginBottom: theme.spacing.xs,
      backgroundColor: theme.colors.surface,
      borderRadius: theme.spacing.borderRadius.lg,
    },
    menuIcon: {
      marginRight: theme.isRTL ? 0 : theme.spacing.md,
      marginLeft: theme.isRTL ? theme.spacing.md : 0,
    },
    menuText: {
      ...theme.typography.textStyles.body,
      color: theme.colors.text,
      flex: 1,
      textAlign: theme.isRTL ? 'right' : 'left',
    },
    menuArrow: {
      marginLeft: theme.isRTL ? 0 : theme.spacing.sm,
      marginRight: theme.isRTL ? theme.spacing.sm : 0,
    },
    themeSection: {
      marginBottom: theme.spacing.lg,
    },
    themeItem: {
      flexDirection: theme.isRTL ? 'row-reverse' : 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: theme.spacing.lg,
      backgroundColor: theme.colors.surface,
      borderRadius: theme.spacing.borderRadius.lg,
    },
    themeLeft: {
      flexDirection: theme.isRTL ? 'row-reverse' : 'row',
      alignItems: 'center',
    },
    themeText: {
      ...theme.typography.textStyles.body,
      color: theme.colors.text,
      marginLeft: theme.isRTL ? 0 : theme.spacing.md,
      marginRight: theme.isRTL ? theme.spacing.md : 0,
    },
    themeValue: {
      ...theme.typography.textStyles.bodySmall,
      color: theme.colors.textSecondary,
    },
    logoutButton: {
      marginTop: theme.spacing.lg,
    },
    versionText: {
      ...theme.typography.textStyles.caption,
      color: theme.colors.textTertiary,
      textAlign: 'center',
      marginTop: theme.spacing.lg,
    },
  });

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getUserTypeText = (userType: string) => {
    return userType === 'provider' ? 'مقدم خدمة' : 'عميل';
  };

  const renderMenuItem = (item: typeof menuItems[0]) => (
    <TouchableOpacity
      key={item.id}
      style={styles.menuItem}
      onPress={item.onPress}
      activeOpacity={0.8}
    >
      <Ionicons
        name={item.icon as any}
        size={theme.spacing.iconSize.lg}
        color={theme.colors.textSecondary}
        style={styles.menuIcon}
      />
      <Text style={styles.menuText}>{item.title}</Text>
      {item.showArrow && (
        <Ionicons
          name={theme.isRTL ? 'chevron-back' : 'chevron-forward'}
          size={theme.spacing.iconSize.md}
          color={theme.colors.textTertiary}
          style={styles.menuArrow}
        />
      )}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title={Strings.profile.title}
        rightIcon="settings"
        onRightIconPress={() => console.log('Settings')}
      />
      
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        <Card style={styles.profileSection}>
          <View style={styles.avatarContainer}>
            <Text style={styles.avatarText}>
              {getInitials(userInfo.name)}
            </Text>
          </View>
          
          <Text style={styles.userName}>{userInfo.name}</Text>
          <Text style={styles.userEmail}>{userInfo.email}</Text>
          <Text style={styles.userType}>
            {getUserTypeText(userInfo.userType)}
          </Text>
          
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{userInfo.rating}</Text>
              <Text style={styles.statLabel}>{Strings.profile.rating}</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{userInfo.completedOrders}</Text>
              <Text style={styles.statLabel}>{Strings.profile.completedOrders}</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{userInfo.memberSince}</Text>
              <Text style={styles.statLabel}>{Strings.profile.memberSince}</Text>
            </View>
          </View>
        </Card>

        <View style={styles.menuSection}>
          {menuItems.map(renderMenuItem)}
        </View>

        <View style={styles.themeSection}>
          <TouchableOpacity
            style={styles.themeItem}
            onPress={toggleTheme}
            activeOpacity={0.8}
          >
            <View style={styles.themeLeft}>
              <Ionicons
                name="color-palette-outline"
                size={theme.spacing.iconSize.lg}
                color={theme.colors.textSecondary}
              />
              <Text style={styles.themeText}>المظهر</Text>
            </View>
            <Text style={styles.themeValue}>{getThemeText()}</Text>
          </TouchableOpacity>
        </View>

        <Button
          title={Strings.profile.logout}
          onPress={handleLogout}
          variant="outline"
          fullWidth
          style={styles.logoutButton}
        />

        <Text style={styles.versionText}>
          {Strings.profile.version} 1.0.0
        </Text>
      </ScrollView>
    </SafeAreaView>
  );
}
