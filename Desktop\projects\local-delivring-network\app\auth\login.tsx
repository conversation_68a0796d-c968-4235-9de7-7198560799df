import { router } from 'expo-router';
import React, { useState } from 'react';
import {
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StyleSheet,
    Text,
    View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { <PERSON><PERSON>, <PERSON><PERSON>, TextInput } from '../../components/ui';
import { Strings } from '../../constants';
import { useTheme } from '../../contexts/ThemeContext';

interface FormData {
  email: string;
  password: string;
}

interface FormErrors {
  email?: string;
  password?: string;
}

export default function Login() {
  const { theme } = useTheme();
  const [formData, setFormData] = useState<FormData>({
    email: '',
    password: '',
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [loading, setLoading] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = Strings.auth.validation.emailRequired;
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = Strings.auth.validation.emailInvalid;
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = Strings.auth.validation.passwordRequired;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      // TODO: Implement actual login logic
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call
      router.replace('/(tabs)');
    } catch (error) {
      console.error('Login error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleBackPress = () => {
    router.back();
  };

  const handleRegisterPress = () => {
    router.push('/auth/register');
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollContainer: {
      flexGrow: 1,
      padding: theme.spacing.screen.horizontal,
    },
    contentContainer: {
      flex: 1,
      justifyContent: 'center',
      paddingVertical: theme.spacing['4xl'],
    },
    titleContainer: {
      alignItems: 'center',
      marginBottom: theme.spacing['4xl'],
    },
    title: {
      ...theme.typography.textStyles.h1,
      color: theme.colors.text,
      textAlign: 'center',
      marginBottom: theme.spacing.sm,
    },
    subtitle: {
      ...theme.typography.textStyles.body,
      color: theme.colors.textSecondary,
      textAlign: 'center',
    },
    formContainer: {
      gap: theme.spacing.lg,
      marginBottom: theme.spacing['3xl'],
    },
    forgotPasswordContainer: {
      alignItems: theme.isRTL ? 'flex-start' : 'flex-end',
      marginTop: -theme.spacing.sm,
    },
    forgotPasswordText: {
      ...theme.typography.textStyles.bodySmall,
      color: theme.colors.primary,
      fontWeight: '600',
    },
    buttonContainer: {
      gap: theme.spacing.md,
    },
    registerContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: theme.spacing.lg,
      gap: theme.spacing.sm,
    },
    registerText: {
      ...theme.typography.textStyles.body,
      color: theme.colors.textSecondary,
    },
    registerLink: {
      ...theme.typography.textStyles.body,
      color: theme.colors.primary,
      fontWeight: '600',
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title={Strings.auth.loginTitle}
        showBackButton
        onBackPress={handleBackPress}
      />
      
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.contentContainer}>
            <View style={styles.titleContainer}>
              <Text style={styles.title}>{Strings.auth.loginTitle}</Text>
              <Text style={styles.subtitle}>{Strings.auth.loginSubtitle}</Text>
            </View>

            <View style={styles.formContainer}>
              <TextInput
                label={Strings.auth.email}
                placeholder={Strings.auth.emailPlaceholder}
                value={formData.email}
                onChangeText={(value) => handleInputChange('email', value)}
                error={errors.email}
                keyboardType="email-address"
                autoCapitalize="none"
                leftIcon="mail"
                required
              />

              <TextInput
                label={Strings.auth.password}
                placeholder={Strings.auth.passwordPlaceholder}
                value={formData.password}
                onChangeText={(value) => handleInputChange('password', value)}
                error={errors.password}
                secureTextEntry
                leftIcon="lock-closed"
                required
              />

              <View style={styles.forgotPasswordContainer}>
                <Text style={styles.forgotPasswordText}>
                  {Strings.auth.forgotPassword}
                </Text>
              </View>
            </View>

            <View style={styles.buttonContainer}>
              <Button
                title={Strings.auth.loginButton}
                onPress={handleLogin}
                loading={loading}
                fullWidth
              />
              
              <View style={styles.registerContainer}>
                <Text style={styles.registerText}>{Strings.auth.noAccount}</Text>
                <Text style={styles.registerLink} onPress={handleRegisterPress}>
                  {Strings.auth.createAccount}
                </Text>
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
