import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Card, Header } from '../../components/ui';
import { Strings } from '../../constants';
import { useTheme } from '../../contexts/ThemeContext';

type NotificationType = 'order' | 'promotion' | 'system';

interface Notification {
  id: number;
  type: NotificationType;
  title: string;
  message: string;
  timestamp: string;
  isRead: boolean;
  icon: string;
  color: string;
}

export default function Notifications() {
  const { theme } = useTheme();
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: 1,
      type: 'order',
      title: 'تم تأكيد طلبك',
      message: 'تم تأكيد طلبك من مطعم الأصالة وسيتم التوصيل خلال 30 دقيقة',
      timestamp: '10:30',
      isRead: false,
      icon: 'checkmark-circle',
      color: theme.colors.success,
    },
    {
      id: 2,
      type: 'order',
      title: 'الطلب في الطريق',
      message: 'السائق محمد في طريقه إليك. يمكنك تتبع الطلب من الخريطة',
      timestamp: '09:45',
      isRead: false,
      icon: 'bicycle',
      color: theme.colors.primary,
    },
    {
      id: 3,
      type: 'promotion',
      title: 'عرض خاص',
      message: 'خصم 20% على جميع طلبات التوصيل اليوم فقط!',
      timestamp: 'أمس',
      isRead: true,
      icon: 'pricetag',
      color: theme.colors.accent,
    },
    {
      id: 4,
      type: 'system',
      title: 'تحديث التطبيق',
      message: 'يتوفر تحديث جديد للتطبيق مع ميزات محسنة',
      timestamp: 'أمس',
      isRead: true,
      icon: 'download',
      color: theme.colors.info,
    },
    {
      id: 5,
      type: 'order',
      title: 'تم تسليم الطلب',
      message: 'تم تسليم طلبك بنجاح. نتمنى أن تكون راضياً عن الخدمة',
      timestamp: 'منذ يومين',
      isRead: true,
      icon: 'checkmark-done',
      color: theme.colors.success,
    },
  ]);

  const unreadCount = notifications.filter(n => !n.isRead).length;

  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, isRead: true }))
    );
  };

  const markAsRead = (id: number) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id
          ? { ...notification, isRead: true }
          : notification
      )
    );
  };

  const getNotificationTypeText = (type: NotificationType) => {
    switch (type) {
      case 'order':
        return Strings.notifications.orderUpdates;
      case 'promotion':
        return Strings.notifications.promotions;
      case 'system':
        return Strings.notifications.system;
      default:
        return '';
    }
  };

  const groupNotificationsByDate = (notifications: Notification[]) => {
    const groups: { [key: string]: Notification[] } = {};
    
    notifications.forEach(notification => {
      let dateKey = '';
      if (notification.timestamp.includes(':')) {
        dateKey = Strings.notifications.today;
      } else if (notification.timestamp === 'أمس') {
        dateKey = Strings.notifications.yesterday;
      } else {
        dateKey = Strings.notifications.thisWeek;
      }
      
      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      groups[dateKey].push(notification);
    });
    
    return groups;
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollContainer: {
      padding: theme.spacing.screen.horizontal,
      paddingBottom: theme.spacing['4xl'],
    },
    headerActions: {
      flexDirection: theme.isRTL ? 'row-reverse' : 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: theme.spacing.screen.horizontal,
      paddingVertical: theme.spacing.md,
      backgroundColor: theme.colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    unreadCount: {
      ...theme.typography.textStyles.bodySmall,
      color: theme.colors.textSecondary,
    },
    markAllReadButton: {
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
    },
    markAllReadText: {
      ...theme.typography.textStyles.bodySmall,
      color: theme.colors.primary,
      fontWeight: '600',
    },
    dateSection: {
      marginBottom: theme.spacing.lg,
    },
    dateHeader: {
      ...theme.typography.textStyles.h4,
      color: theme.colors.text,
      marginBottom: theme.spacing.md,
      textAlign: theme.isRTL ? 'right' : 'left',
    },
    notificationCard: {
      marginBottom: theme.spacing.sm,
      padding: theme.spacing.lg,
      backgroundColor: theme.colors.surface,
    },
    unreadNotificationCard: {
      backgroundColor: theme.colors.primaryLight + '05',
      borderLeftWidth: theme.isRTL ? 0 : 4,
      borderRightWidth: theme.isRTL ? 4 : 0,
      borderLeftColor: theme.isRTL ? 'transparent' : theme.colors.primary,
      borderRightColor: theme.isRTL ? theme.colors.primary : 'transparent',
    },
    notificationContent: {
      flexDirection: theme.isRTL ? 'row-reverse' : 'row',
      alignItems: 'flex-start',
    },
    iconContainer: {
      width: 40,
      height: 40,
      borderRadius: 20,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: theme.isRTL ? 0 : theme.spacing.md,
      marginLeft: theme.isRTL ? theme.spacing.md : 0,
    },
    notificationInfo: {
      flex: 1,
    },
    notificationHeader: {
      flexDirection: theme.isRTL ? 'row-reverse' : 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: theme.spacing.xs,
    },
    notificationTitle: {
      ...theme.typography.textStyles.body,
      color: theme.colors.text,
      fontWeight: '600',
      flex: 1,
      textAlign: theme.isRTL ? 'right' : 'left',
    },
    timestamp: {
      ...theme.typography.textStyles.caption,
      color: theme.colors.textSecondary,
      marginLeft: theme.isRTL ? 0 : theme.spacing.sm,
      marginRight: theme.isRTL ? theme.spacing.sm : 0,
    },
    notificationMessage: {
      ...theme.typography.textStyles.bodySmall,
      color: theme.colors.textSecondary,
      lineHeight: 20,
      marginBottom: theme.spacing.xs,
      textAlign: theme.isRTL ? 'right' : 'left',
    },
    notificationType: {
      ...theme.typography.textStyles.caption,
      color: theme.colors.textTertiary,
      textAlign: theme.isRTL ? 'right' : 'left',
    },
    unreadIndicator: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: theme.colors.primary,
      marginLeft: theme.isRTL ? 0 : theme.spacing.sm,
      marginRight: theme.isRTL ? theme.spacing.sm : 0,
    },
    emptyState: {
      alignItems: 'center',
      padding: theme.spacing['4xl'],
      marginTop: theme.spacing['4xl'],
    },
    emptyStateIcon: {
      marginBottom: theme.spacing.lg,
    },
    emptyStateTitle: {
      ...theme.typography.textStyles.h3,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      marginBottom: theme.spacing.sm,
    },
    emptyStateText: {
      ...theme.typography.textStyles.body,
      color: theme.colors.textSecondary,
      textAlign: 'center',
    },
  });

  const renderNotification = (notification: Notification) => (
    <Card
      key={notification.id}
      style={[
        styles.notificationCard,
        !notification.isRead && styles.unreadNotificationCard,
      ]}
      onPress={() => markAsRead(notification.id)}
    >
      <View style={styles.notificationContent}>
        <View
          style={[
            styles.iconContainer,
            { backgroundColor: notification.color + '20' },
          ]}
        >
          <Ionicons
            name={notification.icon as any}
            size={theme.spacing.iconSize.lg}
            color={notification.color}
          />
        </View>
        
        <View style={styles.notificationInfo}>
          <View style={styles.notificationHeader}>
            <Text style={styles.notificationTitle}>
              {notification.title}
            </Text>
            <View style={{ flexDirection: theme.isRTL ? 'row-reverse' : 'row', alignItems: 'center' }}>
              <Text style={styles.timestamp}>{notification.timestamp}</Text>
              {!notification.isRead && <View style={styles.unreadIndicator} />}
            </View>
          </View>
          
          <Text style={styles.notificationMessage}>
            {notification.message}
          </Text>
          
          <Text style={styles.notificationType}>
            {getNotificationTypeText(notification.type)}
          </Text>
        </View>
      </View>
    </Card>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons
        name="notifications-outline"
        size={theme.spacing.iconSize['3xl']}
        color={theme.colors.textTertiary}
        style={styles.emptyStateIcon}
      />
      <Text style={styles.emptyStateTitle}>
        {Strings.notifications.noNotifications}
      </Text>
      <Text style={styles.emptyStateText}>
        ستظهر إشعاراتك هنا عند وصولها
      </Text>
    </View>
  );

  const groupedNotifications = groupNotificationsByDate(notifications);

  return (
    <SafeAreaView style={styles.container}>
      <Header title={Strings.notifications.title} />
      
      {notifications.length > 0 && (
        <View style={styles.headerActions}>
          <Text style={styles.unreadCount}>
            {unreadCount > 0 ? `${unreadCount} إشعار غير مقروء` : 'جميع الإشعارات مقروءة'}
          </Text>
          {unreadCount > 0 && (
            <TouchableOpacity
              style={styles.markAllReadButton}
              onPress={markAllAsRead}
              activeOpacity={0.8}
            >
              <Text style={styles.markAllReadText}>
                {Strings.notifications.markAllRead}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      )}
      
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        {notifications.length > 0 ? (
          Object.entries(groupedNotifications).map(([date, notificationGroup]) => (
            <View key={date} style={styles.dateSection}>
              <Text style={styles.dateHeader}>{date}</Text>
              {notificationGroup.map(renderNotification)}
            </View>
          ))
        ) : (
          renderEmptyState()
        )}
      </ScrollView>
    </SafeAreaView>
  );
}
