// Moroccan-inspired color palette for local delivery network
export const Colors = {
  light: {
    // Primary colors inspired by Moroccan flag and traditional colors
    primary: '#C1272D', // Moroccan red
    primaryLight: '#E8454A',
    primaryDark: '#8B1C20',
    
    // Secondary colors inspired by Moroccan architecture and nature
    secondary: '#2E7D32', // Moroccan green
    secondaryLight: '#4CAF50',
    secondaryDark: '#1B5E20',
    
    // Accent colors inspired by Moroccan tiles and crafts
    accent: '#FF8F00', // Moroccan gold/amber
    accentLight: '#FFB74D',
    accentDark: '#E65100',
    
    // Background colors
    background: '#FFFFFF',
    backgroundSecondary: '#F8F9FA',
    backgroundTertiary: '#F1F3F4',
    
    // Surface colors
    surface: '#FFFFFF',
    surfaceVariant: '#F5F5F5',
    
    // Text colors
    text: '#1A1A1A',
    textSecondary: '#666666',
    textTertiary: '#999999',
    textOnPrimary: '#FFFFFF',
    textOnSecondary: '#FFFFFF',
    textOnAccent: '#FFFFFF',
    
    // Border and divider colors
    border: '#E0E0E0',
    divider: '#EEEEEE',
    
    // Status colors
    success: '#4CAF50',
    warning: '#FF9800',
    error: '#F44336',
    info: '#2196F3',
    
    // Shadow colors
    shadow: 'rgba(0, 0, 0, 0.1)',
    shadowDark: 'rgba(0, 0, 0, 0.2)',
  },
  
  dark: {
    // Primary colors adapted for dark theme
    primary: '#E8454A', // Slightly lighter red for better contrast
    primaryLight: '#FF6B70',
    primaryDark: '#C1272D',
    
    // Secondary colors adapted for dark theme
    secondary: '#4CAF50', // Brighter green for dark theme
    secondaryLight: '#81C784',
    secondaryDark: '#2E7D32',
    
    // Accent colors adapted for dark theme
    accent: '#FFB74D', // Brighter gold for dark theme
    accentLight: '#FFCC80',
    accentDark: '#FF8F00',
    
    // Background colors for dark theme
    background: '#121212',
    backgroundSecondary: '#1E1E1E',
    backgroundTertiary: '#2A2A2A',
    
    // Surface colors for dark theme
    surface: '#1E1E1E',
    surfaceVariant: '#2A2A2A',
    
    // Text colors for dark theme
    text: '#FFFFFF',
    textSecondary: '#B3B3B3',
    textTertiary: '#808080',
    textOnPrimary: '#FFFFFF',
    textOnSecondary: '#FFFFFF',
    textOnAccent: '#000000',
    
    // Border and divider colors for dark theme
    border: '#404040',
    divider: '#333333',
    
    // Status colors for dark theme
    success: '#66BB6A',
    warning: '#FFB74D',
    error: '#EF5350',
    info: '#42A5F5',
    
    // Shadow colors for dark theme
    shadow: 'rgba(0, 0, 0, 0.3)',
    shadowDark: 'rgba(0, 0, 0, 0.5)',
  },
};

export type ColorScheme = keyof typeof Colors;
export type ColorName = keyof typeof Colors.light;
