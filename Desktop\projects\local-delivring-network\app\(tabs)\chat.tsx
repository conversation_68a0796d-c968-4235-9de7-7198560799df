import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import {
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Card, Header } from '../../components/ui';
import { Strings } from '../../constants';
import { useTheme } from '../../contexts/ThemeContext';

export default function Chat() {
  const { theme } = useTheme();

  const conversations = [
    {
      id: 1,
      name: 'محمد الأمين',
      lastMessage: 'شكراً لك، الطلب وصل بسلامة',
      timestamp: '10:30',
      unreadCount: 0,
      isOnline: true,
      avatar: null,
      userType: 'provider',
    },
    {
      id: 2,
      name: 'فاطمة الزهراء',
      lastMessage: 'متى سيصل الطلب؟',
      timestamp: '09:45',
      unreadCount: 2,
      isOnline: false,
      avatar: null,
      userType: 'seeker',
    },
    {
      id: 3,
      name: 'عبد الرحمن',
      lastMessage: 'الطلب في الطريق إليك',
      timestamp: 'أمس',
      unreadCount: 0,
      isOnline: true,
      avatar: null,
      userType: 'provider',
    },
    {
      id: 4,
      name: 'خديجة',
      lastMessage: 'هل يمكن تغيير العنوان؟',
      timestamp: 'أمس',
      unreadCount: 1,
      isOnline: false,
      avatar: null,
      userType: 'seeker',
    },
  ];

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollContainer: {
      padding: theme.spacing.screen.horizontal,
      paddingBottom: theme.spacing['4xl'],
    },
    conversationCard: {
      marginBottom: theme.spacing.sm,
      padding: theme.spacing.lg,
    },
    conversationContent: {
      flexDirection: theme.isRTL ? 'row-reverse' : 'row',
      alignItems: 'center',
    },
    avatarContainer: {
      position: 'relative',
      marginRight: theme.isRTL ? 0 : theme.spacing.md,
      marginLeft: theme.isRTL ? theme.spacing.md : 0,
    },
    avatar: {
      width: 50,
      height: 50,
      borderRadius: 25,
      backgroundColor: theme.colors.surfaceVariant,
      alignItems: 'center',
      justifyContent: 'center',
    },
    avatarText: {
      ...theme.typography.textStyles.body,
      color: theme.colors.text,
      fontWeight: '600',
    },
    onlineIndicator: {
      position: 'absolute',
      bottom: 2,
      right: theme.isRTL ? undefined : 2,
      left: theme.isRTL ? 2 : undefined,
      width: 12,
      height: 12,
      borderRadius: 6,
      backgroundColor: theme.colors.success,
      borderWidth: 2,
      borderColor: theme.colors.surface,
    },
    conversationInfo: {
      flex: 1,
    },
    conversationHeader: {
      flexDirection: theme.isRTL ? 'row-reverse' : 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: theme.spacing.xs,
    },
    userName: {
      ...theme.typography.textStyles.body,
      color: theme.colors.text,
      fontWeight: '600',
      textAlign: theme.isRTL ? 'right' : 'left',
    },
    timestamp: {
      ...theme.typography.textStyles.caption,
      color: theme.colors.textSecondary,
    },
    messageContainer: {
      flexDirection: theme.isRTL ? 'row-reverse' : 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    lastMessage: {
      ...theme.typography.textStyles.bodySmall,
      color: theme.colors.textSecondary,
      flex: 1,
      textAlign: theme.isRTL ? 'right' : 'left',
    },
    unreadBadge: {
      backgroundColor: theme.colors.primary,
      borderRadius: 10,
      minWidth: 20,
      height: 20,
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: theme.spacing.xs,
    },
    unreadCount: {
      ...theme.typography.textStyles.caption,
      color: theme.colors.textOnPrimary,
      fontWeight: '600',
      fontSize: 12,
    },
    userTypeIndicator: {
      flexDirection: theme.isRTL ? 'row-reverse' : 'row',
      alignItems: 'center',
      gap: theme.spacing.xs,
      marginTop: theme.spacing.xs,
    },
    userTypeText: {
      ...theme.typography.textStyles.caption,
      color: theme.colors.textTertiary,
      fontSize: 10,
    },
    emptyState: {
      alignItems: 'center',
      padding: theme.spacing['4xl'],
      marginTop: theme.spacing['4xl'],
    },
    emptyStateIcon: {
      marginBottom: theme.spacing.lg,
    },
    emptyStateTitle: {
      ...theme.typography.textStyles.h3,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      marginBottom: theme.spacing.sm,
    },
    emptyStateText: {
      ...theme.typography.textStyles.body,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      marginBottom: theme.spacing.lg,
    },
    newChatButton: {
      backgroundColor: theme.colors.primary,
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.sm,
      borderRadius: theme.spacing.borderRadius.full,
    },
    newChatButtonText: {
      ...theme.typography.textStyles.bodySmall,
      color: theme.colors.textOnPrimary,
      fontWeight: '600',
    },
  });

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getUserTypeIcon = (userType: string) => {
    return userType === 'provider' ? 'bicycle' : 'person';
  };

  const getUserTypeText = (userType: string) => {
    return userType === 'provider' ? 'مقدم خدمة' : 'عميل';
  };

  const renderConversation = (conversation: typeof conversations[0]) => (
    <Card
      key={conversation.id}
      style={styles.conversationCard}
      onPress={() => console.log('Open chat', conversation.id)}
    >
      <View style={styles.conversationContent}>
        <View style={styles.avatarContainer}>
          <View style={styles.avatar}>
            <Text style={styles.avatarText}>
              {getInitials(conversation.name)}
            </Text>
          </View>
          {conversation.isOnline && <View style={styles.onlineIndicator} />}
        </View>
        
        <View style={styles.conversationInfo}>
          <View style={styles.conversationHeader}>
            <Text style={styles.userName}>{conversation.name}</Text>
            <Text style={styles.timestamp}>{conversation.timestamp}</Text>
          </View>
          
          <View style={styles.messageContainer}>
            <Text style={styles.lastMessage} numberOfLines={1}>
              {conversation.lastMessage}
            </Text>
            {conversation.unreadCount > 0 && (
              <View style={styles.unreadBadge}>
                <Text style={styles.unreadCount}>
                  {conversation.unreadCount}
                </Text>
              </View>
            )}
          </View>
          
          <View style={styles.userTypeIndicator}>
            <Ionicons
              name={getUserTypeIcon(conversation.userType) as any}
              size={12}
              color={theme.colors.textTertiary}
            />
            <Text style={styles.userTypeText}>
              {getUserTypeText(conversation.userType)}
            </Text>
          </View>
        </View>
      </View>
    </Card>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons
        name="chatbubbles-outline"
        size={theme.spacing.iconSize['3xl']}
        color={theme.colors.textTertiary}
        style={styles.emptyStateIcon}
      />
      <Text style={styles.emptyStateTitle}>{Strings.chat.noChats}</Text>
      <Text style={styles.emptyStateText}>
        {Strings.chat.startConversation}
      </Text>
      <TouchableOpacity
        style={styles.newChatButton}
        onPress={() => console.log('Start new chat')}
        activeOpacity={0.8}
      >
        <Text style={styles.newChatButtonText}>
          {Strings.chat.newMessage}
        </Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title={Strings.chat.title}
        rightIcon="create"
        onRightIconPress={() => console.log('New message')}
      />
      
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        {conversations.length > 0 ? (
          conversations.map(renderConversation)
        ) : (
          renderEmptyState()
        )}
      </ScrollView>
    </SafeAreaView>
  );
}
