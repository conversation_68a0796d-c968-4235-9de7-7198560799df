import React, { useState } from 'react';
import {
  TextInput as RNTextInput,
  View,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';

interface TextInputProps {
  label?: string;
  placeholder?: string;
  value: string;
  onChangeText: (text: string) => void;
  error?: string;
  disabled?: boolean;
  secureTextEntry?: boolean;
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad';
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
  multiline?: boolean;
  numberOfLines?: number;
  style?: ViewStyle;
  inputStyle?: TextStyle;
  leftIcon?: string;
  rightIcon?: string;
  onRightIconPress?: () => void;
  required?: boolean;
}

export const TextInput: React.FC<TextInputProps> = ({
  label,
  placeholder,
  value,
  onChangeText,
  error,
  disabled = false,
  secureTextEntry = false,
  keyboardType = 'default',
  autoCapitalize = 'sentences',
  multiline = false,
  numberOfLines = 1,
  style,
  inputStyle,
  leftIcon,
  rightIcon,
  onRightIconPress,
  required = false,
}) => {
  const { theme } = useTheme();
  const [isFocused, setIsFocused] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const getContainerStyle = (): ViewStyle => ({
    marginBottom: theme.spacing.component.inputMargin,
  });

  const getLabelStyle = (): TextStyle => ({
    ...theme.typography.textStyles.label,
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
    textAlign: theme.isRTL ? 'right' : 'left',
  });

  const getInputContainerStyle = (): ViewStyle => {
    let baseStyle = theme.components.input.default;
    
    if (isFocused) {
      baseStyle = { ...baseStyle, ...theme.components.input.focused };
    }
    
    if (error) {
      baseStyle = { ...baseStyle, ...theme.components.input.error };
    }
    
    return {
      ...baseStyle,
      opacity: disabled ? 0.6 : 1,
      flexDirection: theme.isRTL ? 'row-reverse' : 'row',
      alignItems: multiline ? 'flex-start' : 'center',
      minHeight: multiline ? numberOfLines * 20 + 24 : 44,
    };
  };

  const getInputStyle = (): TextStyle => ({
    flex: 1,
    color: theme.colors.text,
    fontSize: theme.typography.fontSize.base,
    fontFamily: theme.typography.fontFamily.regular,
    textAlign: theme.isRTL ? 'right' : 'left',
    writingDirection: theme.isRTL ? 'rtl' : 'ltr',
    paddingTop: multiline ? theme.spacing.sm : 0,
    paddingBottom: multiline ? theme.spacing.sm : 0,
  });

  const getErrorStyle = (): TextStyle => ({
    ...theme.typography.textStyles.caption,
    color: theme.colors.error,
    marginTop: theme.spacing.xs,
    textAlign: theme.isRTL ? 'right' : 'left',
  });

  const getIconStyle = () => ({
    marginHorizontal: theme.spacing.sm,
    color: isFocused ? theme.colors.primary : theme.colors.textSecondary,
  });

  const handleTogglePassword = () => {
    setShowPassword(!showPassword);
  };

  const renderIcon = (iconName: string, onPress?: () => void) => (
    <TouchableOpacity onPress={onPress} disabled={!onPress}>
      <Ionicons
        name={iconName as any}
        size={theme.spacing.iconSize.md}
        style={getIconStyle()}
      />
    </TouchableOpacity>
  );

  return (
    <View style={[getContainerStyle(), style]}>
      {label && (
        <Text style={getLabelStyle()}>
          {label}
          {required && <Text style={{ color: theme.colors.error }}> *</Text>}
        </Text>
      )}
      
      <View style={getInputContainerStyle()}>
        {leftIcon && renderIcon(leftIcon)}
        
        <RNTextInput
          style={[getInputStyle(), inputStyle]}
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          placeholderTextColor={theme.colors.textTertiary}
          editable={!disabled}
          secureTextEntry={secureTextEntry && !showPassword}
          keyboardType={keyboardType}
          autoCapitalize={autoCapitalize}
          multiline={multiline}
          numberOfLines={numberOfLines}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
        />
        
        {secureTextEntry && renderIcon(
          showPassword ? 'eye-off' : 'eye',
          handleTogglePassword
        )}
        
        {rightIcon && !secureTextEntry && renderIcon(rightIcon, onRightIconPress)}
      </View>
      
      {error && <Text style={getErrorStyle()}>{error}</Text>}
    </View>
  );
};
