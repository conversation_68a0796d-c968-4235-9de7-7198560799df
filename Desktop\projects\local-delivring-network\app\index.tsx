import { router } from 'expo-router';
import { useEffect } from 'react';
import { View } from 'react-native';
import { Loading } from '../components/ui';

export default function Index() {
  useEffect(() => {
    // Check if user is authenticated
    // For now, always redirect to welcome screen
    const timer = setTimeout(() => {
      router.replace('/welcome');
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <Loading text="جاري التحميل..." />
    </View>
  );
}
