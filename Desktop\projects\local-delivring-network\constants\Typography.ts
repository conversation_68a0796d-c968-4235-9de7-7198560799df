// Typography system optimized for Arabic text and RTL support
export const Typography = {
  // Font families - prioritizing Arabic-friendly fonts
  fontFamily: {
    regular: 'System', // System font supports Arabic well
    medium: 'System', 
    bold: 'System',
    // For custom Arabic fonts, you can add them here
    arabic: 'System', // Can be replaced with custom Arabic font
  },
  
  // Font sizes following Material Design scale
  fontSize: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
    '5xl': 48,
  },
  
  // Line heights optimized for Arabic text
  lineHeight: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
    loose: 1.8,
  },
  
  // Font weights
  fontWeight: {
    light: '300' as const,
    normal: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
    extrabold: '800' as const,
  },
  
  // Text styles for common use cases
  textStyles: {
    // Headers
    h1: {
      fontSize: 30,
      fontWeight: '700' as const,
      lineHeight: 36,
    },
    h2: {
      fontSize: 24,
      fontWeight: '600' as const,
      lineHeight: 31,
    },
    h3: {
      fontSize: 20,
      fontWeight: '600' as const,
      lineHeight: 28,
    },
    h4: {
      fontSize: 18,
      fontWeight: '500' as const,
      lineHeight: 25,
    },
    
    // Body text
    body: {
      fontSize: 16,
      fontWeight: '400' as const,
      lineHeight: 24,
    },
    bodySmall: {
      fontSize: 14,
      fontWeight: '400' as const,
      lineHeight: 20,
    },
    
    // UI text
    button: {
      fontSize: 16,
      fontWeight: '600' as const,
      lineHeight: 19,
    },
    caption: {
      fontSize: 12,
      fontWeight: '400' as const,
      lineHeight: 16,
    },
    label: {
      fontSize: 14,
      fontWeight: '500' as const,
      lineHeight: 17,
    },
    
    // Input text
    input: {
      fontSize: 16,
      fontWeight: '400' as const,
      lineHeight: 22,
    },
    placeholder: {
      fontSize: 16,
      fontWeight: '400' as const,
      lineHeight: 22,
    },
  },
};

export type FontSize = keyof typeof Typography.fontSize;
export type TextStyle = keyof typeof Typography.textStyles;
