import { router } from 'expo-router';
import React, { useState } from 'react';
import {
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StyleSheet,
    Text,
    View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { <PERSON><PERSON>, Card, Header, TextInput } from '../../components/ui';
import { Strings } from '../../constants';
import { useTheme } from '../../contexts/ThemeContext';

type UserType = 'provider' | 'seeker';

interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  password: string;
  confirmPassword: string;
  userType: UserType | null;
  agreeToTerms: boolean;
}

interface FormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  password?: string;
  confirmPassword?: string;
  userType?: string;
  agreeToTerms?: string;
}

export default function Register() {
  const { theme } = useTheme();
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    userType: null,
    agreeToTerms: false,
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [loading, setLoading] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // First name validation
    if (!formData.firstName.trim()) {
      newErrors.firstName = Strings.auth.validation.firstNameRequired;
    }

    // Last name validation
    if (!formData.lastName.trim()) {
      newErrors.lastName = Strings.auth.validation.lastNameRequired;
    }

    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = Strings.auth.validation.emailRequired;
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = Strings.auth.validation.emailInvalid;
    }

    // Phone validation
    if (!formData.phone.trim()) {
      newErrors.phone = Strings.auth.validation.phoneRequired;
    } else if (!/^[0-9+\-\s()]+$/.test(formData.phone)) {
      newErrors.phone = Strings.auth.validation.phoneInvalid;
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = Strings.auth.validation.passwordRequired;
    } else if (formData.password.length < 8) {
      newErrors.password = Strings.auth.validation.passwordTooShort;
    }

    // Confirm password validation
    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = Strings.auth.validation.passwordsNotMatch;
    }

    // Terms validation
    if (!formData.agreeToTerms) {
      newErrors.agreeToTerms = Strings.auth.validation.termsRequired;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleRegister = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      // TODO: Implement actual registration logic
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call
      router.replace('/(tabs)');
    } catch (error) {
      console.error('Registration error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof FormData, value: string | boolean | UserType) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleBackPress = () => {
    router.back();
  };

  const handleLoginPress = () => {
    router.push('/auth/login');
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollContainer: {
      flexGrow: 1,
      padding: theme.spacing.screen.horizontal,
      paddingBottom: theme.spacing['4xl'],
    },
    titleContainer: {
      alignItems: 'center',
      marginVertical: theme.spacing['2xl'],
    },
    title: {
      ...theme.typography.textStyles.h2,
      color: theme.colors.text,
      textAlign: 'center',
      marginBottom: theme.spacing.sm,
    },
    subtitle: {
      ...theme.typography.textStyles.body,
      color: theme.colors.textSecondary,
      textAlign: 'center',
    },
    formContainer: {
      gap: theme.spacing.lg,
      marginBottom: theme.spacing['2xl'],
    },
    userTypeSection: {
      marginBottom: theme.spacing.lg,
    },
    userTypeLabel: {
      ...theme.typography.textStyles.label,
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
      textAlign: theme.isRTL ? 'right' : 'left',
    },
    userTypeContainer: {
      flexDirection: 'column',
      gap: theme.spacing.sm,
    },
    userTypeCard: {
      padding: theme.spacing.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    selectedUserTypeCard: {
      borderColor: theme.colors.primary,
      backgroundColor: theme.colors.primaryLight + '10',
    },
    userTypeText: {
      ...theme.typography.textStyles.body,
      color: theme.colors.text,
      textAlign: theme.isRTL ? 'right' : 'left',
    },
    termsContainer: {
      flexDirection: theme.isRTL ? 'row-reverse' : 'row',
      alignItems: 'flex-start',
      gap: theme.spacing.sm,
    },
    checkbox: {
      width: 20,
      height: 20,
      borderWidth: 2,
      borderColor: theme.colors.border,
      borderRadius: 4,
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: 2,
    },
    checkedCheckbox: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
    },
    checkmark: {
      color: theme.colors.textOnPrimary,
      fontSize: 14,
      fontWeight: 'bold',
    },
    termsText: {
      ...theme.typography.textStyles.bodySmall,
      color: theme.colors.textSecondary,
      flex: 1,
      textAlign: theme.isRTL ? 'right' : 'left',
    },
    termsLink: {
      color: theme.colors.primary,
      fontWeight: '600',
    },
    errorText: {
      ...theme.typography.textStyles.caption,
      color: theme.colors.error,
      marginTop: theme.spacing.xs,
      textAlign: theme.isRTL ? 'right' : 'left',
    },
    buttonContainer: {
      gap: theme.spacing.md,
      marginTop: theme.spacing.lg,
    },
    loginContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: theme.spacing.lg,
      gap: theme.spacing.sm,
    },
    loginText: {
      ...theme.typography.textStyles.body,
      color: theme.colors.textSecondary,
    },
    loginLink: {
      ...theme.typography.textStyles.body,
      color: theme.colors.primary,
      fontWeight: '600',
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title={Strings.auth.registerTitle}
        showBackButton
        onBackPress={handleBackPress}
      />
      
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.titleContainer}>
            <Text style={styles.title}>{Strings.auth.registerTitle}</Text>
            <Text style={styles.subtitle}>{Strings.auth.registerSubtitle}</Text>
          </View>

          <View style={styles.formContainer}>
            <TextInput
              label={Strings.auth.firstName}
              placeholder={Strings.auth.firstNamePlaceholder}
              value={formData.firstName}
              onChangeText={(value) => handleInputChange('firstName', value)}
              error={errors.firstName}
              leftIcon="person"
              required
            />

            <TextInput
              label={Strings.auth.lastName}
              placeholder={Strings.auth.lastNamePlaceholder}
              value={formData.lastName}
              onChangeText={(value) => handleInputChange('lastName', value)}
              error={errors.lastName}
              leftIcon="person"
              required
            />

            <TextInput
              label={Strings.auth.email}
              placeholder={Strings.auth.emailPlaceholder}
              value={formData.email}
              onChangeText={(value) => handleInputChange('email', value)}
              error={errors.email}
              keyboardType="email-address"
              autoCapitalize="none"
              leftIcon="mail"
              required
            />

            <TextInput
              label={Strings.auth.phone}
              placeholder={Strings.auth.phonePlaceholder}
              value={formData.phone}
              onChangeText={(value) => handleInputChange('phone', value)}
              error={errors.phone}
              keyboardType="phone-pad"
              leftIcon="call"
              required
            />

            <TextInput
              label={Strings.auth.password}
              placeholder={Strings.auth.passwordPlaceholder}
              value={formData.password}
              onChangeText={(value) => handleInputChange('password', value)}
              error={errors.password}
              secureTextEntry
              leftIcon="lock-closed"
              required
            />

            <TextInput
              label={Strings.auth.confirmPassword}
              placeholder={Strings.auth.confirmPasswordPlaceholder}
              value={formData.confirmPassword}
              onChangeText={(value) => handleInputChange('confirmPassword', value)}
              error={errors.confirmPassword}
              secureTextEntry
              leftIcon="lock-closed"
              required
            />

            <View style={styles.userTypeSection}>
              <Text style={styles.userTypeLabel}>
                {Strings.welcome.chooseUserType} *
              </Text>
              <View style={styles.userTypeContainer}>
                <Card
                  style={[
                    styles.userTypeCard,
                    formData.userType === 'provider' && styles.selectedUserTypeCard,
                  ]}
                  onPress={() => handleInputChange('userType', 'provider')}
                >
                  <Text style={styles.userTypeText}>
                    {Strings.welcome.serviceProvider}
                  </Text>
                </Card>
                <Card
                  style={[
                    styles.userTypeCard,
                    formData.userType === 'seeker' && styles.selectedUserTypeCard,
                  ]}
                  onPress={() => handleInputChange('userType', 'seeker')}
                >
                  <Text style={styles.userTypeText}>
                    {Strings.welcome.serviceSeeker}
                  </Text>
                </Card>
              </View>
            </View>

            <View>
              <View style={styles.termsContainer}>
                <View
                  style={[
                    styles.checkbox,
                    formData.agreeToTerms && styles.checkedCheckbox,
                  ]}
                  onTouchEnd={() => handleInputChange('agreeToTerms', !formData.agreeToTerms)}
                >
                  {formData.agreeToTerms && <Text style={styles.checkmark}>✓</Text>}
                </View>
                <Text style={styles.termsText}>
                  {Strings.auth.agreeToTerms}
                </Text>
              </View>
              {errors.agreeToTerms && (
                <Text style={styles.errorText}>{errors.agreeToTerms}</Text>
              )}
            </View>
          </View>

          <View style={styles.buttonContainer}>
            <Button
              title={Strings.auth.registerButton}
              onPress={handleRegister}
              loading={loading}
              fullWidth
            />
            
            <View style={styles.loginContainer}>
              <Text style={styles.loginText}>{Strings.auth.haveAccount}</Text>
              <Text style={styles.loginLink} onPress={handleLoginPress}>
                {Strings.auth.loginButton}
              </Text>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
