import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import {
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Card, Header } from '../../components/ui';
import { Strings } from '../../constants';
import { useTheme } from '../../contexts/ThemeContext';

export default function Home() {
  const { theme } = useTheme();

  const quickActions = [
    {
      id: 1,
      title: 'طلب توصيل',
      icon: 'bicycle',
      color: theme.colors.primary,
      onPress: () => console.log('Request delivery'),
    },
    {
      id: 2,
      title: 'تتبع الطلب',
      icon: 'location',
      color: theme.colors.secondary,
      onPress: () => console.log('Track order'),
    },
    {
      id: 3,
      title: 'المحفظة',
      icon: 'wallet',
      color: theme.colors.accent,
      onPress: () => console.log('Wallet'),
    },
    {
      id: 4,
      title: 'الدعم',
      icon: 'help-circle',
      color: theme.colors.info,
      onPress: () => console.log('Support'),
    },
  ];

  const recentOrders = [
    {
      id: 1,
      title: 'توصيل من المطعم الشعبي',
      status: 'مكتمل',
      date: '2024-01-15',
      price: '45 درهم',
    },
    {
      id: 2,
      title: 'توصيل من السوق المركزي',
      status: 'قيد التوصيل',
      date: '2024-01-16',
      price: '32 درهم',
    },
  ];

  const popularServices = [
    {
      id: 1,
      title: 'توصيل الطعام',
      description: 'توصيل سريع من المطاعم',
      icon: 'restaurant',
      rating: 4.8,
    },
    {
      id: 2,
      title: 'توصيل البقالة',
      description: 'مشتريات يومية إلى باب منزلك',
      icon: 'storefront',
      rating: 4.6,
    },
    {
      id: 3,
      title: 'توصيل الأدوية',
      description: 'خدمة طبية موثوقة',
      icon: 'medical',
      rating: 4.9,
    },
  ];

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollContainer: {
      padding: theme.spacing.screen.horizontal,
      paddingBottom: theme.spacing['4xl'],
    },
    welcomeSection: {
      marginBottom: theme.spacing['2xl'],
    },
    welcomeText: {
      ...theme.typography.textStyles.h2,
      color: theme.colors.text,
      textAlign: theme.isRTL ? 'right' : 'left',
      marginBottom: theme.spacing.sm,
    },
    userNameText: {
      color: theme.colors.primary,
    },
    dateText: {
      ...theme.typography.textStyles.bodySmall,
      color: theme.colors.textSecondary,
      textAlign: theme.isRTL ? 'right' : 'left',
    },
    sectionContainer: {
      marginBottom: theme.spacing['2xl'],
    },
    sectionHeader: {
      flexDirection: theme.isRTL ? 'row-reverse' : 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: theme.spacing.lg,
    },
    sectionTitle: {
      ...theme.typography.textStyles.h3,
      color: theme.colors.text,
    },
    viewAllText: {
      ...theme.typography.textStyles.bodySmall,
      color: theme.colors.primary,
      fontWeight: '600',
    },
    quickActionsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
      gap: theme.spacing.md,
    },
    quickActionItem: {
      width: '48%',
      alignItems: 'center',
      padding: theme.spacing.lg,
      borderRadius: theme.spacing.borderRadius.lg,
      backgroundColor: theme.colors.surface,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    quickActionIcon: {
      marginBottom: theme.spacing.sm,
    },
    quickActionText: {
      ...theme.typography.textStyles.bodySmall,
      color: theme.colors.text,
      textAlign: 'center',
      fontWeight: '500',
    },
    orderCard: {
      marginBottom: theme.spacing.md,
      padding: theme.spacing.lg,
    },
    orderHeader: {
      flexDirection: theme.isRTL ? 'row-reverse' : 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: theme.spacing.sm,
    },
    orderTitle: {
      ...theme.typography.textStyles.body,
      color: theme.colors.text,
      fontWeight: '600',
      flex: 1,
      textAlign: theme.isRTL ? 'right' : 'left',
    },
    orderPrice: {
      ...theme.typography.textStyles.body,
      color: theme.colors.primary,
      fontWeight: '600',
    },
    orderFooter: {
      flexDirection: theme.isRTL ? 'row-reverse' : 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    orderStatus: {
      ...theme.typography.textStyles.bodySmall,
      color: theme.colors.secondary,
      fontWeight: '500',
    },
    orderDate: {
      ...theme.typography.textStyles.bodySmall,
      color: theme.colors.textSecondary,
    },
    serviceCard: {
      marginBottom: theme.spacing.md,
      padding: theme.spacing.lg,
    },
    serviceHeader: {
      flexDirection: theme.isRTL ? 'row-reverse' : 'row',
      alignItems: 'center',
      marginBottom: theme.spacing.sm,
    },
    serviceIcon: {
      marginRight: theme.isRTL ? 0 : theme.spacing.md,
      marginLeft: theme.isRTL ? theme.spacing.md : 0,
    },
    serviceContent: {
      flex: 1,
    },
    serviceTitle: {
      ...theme.typography.textStyles.body,
      color: theme.colors.text,
      fontWeight: '600',
      marginBottom: theme.spacing.xs,
      textAlign: theme.isRTL ? 'right' : 'left',
    },
    serviceDescription: {
      ...theme.typography.textStyles.bodySmall,
      color: theme.colors.textSecondary,
      textAlign: theme.isRTL ? 'right' : 'left',
    },
    serviceRating: {
      flexDirection: theme.isRTL ? 'row-reverse' : 'row',
      alignItems: 'center',
      gap: theme.spacing.xs,
    },
    ratingText: {
      ...theme.typography.textStyles.bodySmall,
      color: theme.colors.accent,
      fontWeight: '600',
    },
    emptyState: {
      alignItems: 'center',
      padding: theme.spacing['2xl'],
    },
    emptyStateText: {
      ...theme.typography.textStyles.body,
      color: theme.colors.textSecondary,
      textAlign: 'center',
    },
  });

  const renderQuickAction = (action: typeof quickActions[0]) => (
    <TouchableOpacity
      key={action.id}
      style={styles.quickActionItem}
      onPress={action.onPress}
      activeOpacity={0.8}
    >
      <Ionicons
        name={action.icon as any}
        size={theme.spacing.iconSize['2xl']}
        color={action.color}
        style={styles.quickActionIcon}
      />
      <Text style={styles.quickActionText}>{action.title}</Text>
    </TouchableOpacity>
  );

  const renderOrderCard = (order: typeof recentOrders[0]) => (
    <Card key={order.id} style={styles.orderCard}>
      <View style={styles.orderHeader}>
        <Text style={styles.orderTitle}>{order.title}</Text>
        <Text style={styles.orderPrice}>{order.price}</Text>
      </View>
      <View style={styles.orderFooter}>
        <Text style={styles.orderStatus}>{order.status}</Text>
        <Text style={styles.orderDate}>{order.date}</Text>
      </View>
    </Card>
  );

  const renderServiceCard = (service: typeof popularServices[0]) => (
    <Card key={service.id} style={styles.serviceCard}>
      <View style={styles.serviceHeader}>
        <Ionicons
          name={service.icon as any}
          size={theme.spacing.iconSize.lg}
          color={theme.colors.primary}
          style={styles.serviceIcon}
        />
        <View style={styles.serviceContent}>
          <Text style={styles.serviceTitle}>{service.title}</Text>
          <Text style={styles.serviceDescription}>{service.description}</Text>
        </View>
        <View style={styles.serviceRating}>
          <Ionicons
            name="star"
            size={theme.spacing.iconSize.sm}
            color={theme.colors.accent}
          />
          <Text style={styles.ratingText}>{service.rating}</Text>
        </View>
      </View>
    </Card>
  );

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title={Strings.navigation.home}
        rightIcon="notifications"
        onRightIconPress={() => console.log('Notifications')}
      />
      
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.welcomeSection}>
          <Text style={styles.welcomeText}>
            {Strings.home.welcomeBack}{' '}
            <Text style={styles.userNameText}>أحمد</Text>
          </Text>
          <Text style={styles.dateText}>
            {new Date().toLocaleDateString('ar-MA')}
          </Text>
        </View>

        <View style={styles.sectionContainer}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>{Strings.home.quickActions}</Text>
          </View>
          <View style={styles.quickActionsGrid}>
            {quickActions.map(renderQuickAction)}
          </View>
        </View>

        <View style={styles.sectionContainer}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>{Strings.home.recentOrders}</Text>
            <Text style={styles.viewAllText}>{Strings.home.viewAll}</Text>
          </View>
          {recentOrders.length > 0 ? (
            recentOrders.map(renderOrderCard)
          ) : (
            <View style={styles.emptyState}>
              <Text style={styles.emptyStateText}>
                {Strings.home.noRecentOrders}
              </Text>
            </View>
          )}
        </View>

        <View style={styles.sectionContainer}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>{Strings.home.popularServices}</Text>
            <Text style={styles.viewAllText}>{Strings.home.viewAll}</Text>
          </View>
          {popularServices.map(renderServiceCard)}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
