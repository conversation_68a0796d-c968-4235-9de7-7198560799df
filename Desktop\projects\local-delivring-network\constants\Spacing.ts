// Spacing system for consistent layout
export const Spacing = {
  // Base spacing unit (4px)
  unit: 4,
  
  // Spacing scale
  xs: 4,   // 4px
  sm: 8,   // 8px
  md: 12,  // 12px
  lg: 16,  // 16px
  xl: 20,  // 20px
  '2xl': 24, // 24px
  '3xl': 32, // 32px
  '4xl': 40, // 40px
  '5xl': 48, // 48px
  '6xl': 64, // 64px
  
  // Common spacing values
  padding: {
    xs: 4,
    sm: 8,
    md: 12,
    lg: 16,
    xl: 20,
    '2xl': 24,
    '3xl': 32,
  },
  
  margin: {
    xs: 4,
    sm: 8,
    md: 12,
    lg: 16,
    xl: 20,
    '2xl': 24,
    '3xl': 32,
  },
  
  // Screen padding (safe areas)
  screen: {
    horizontal: 16,
    vertical: 20,
  },
  
  // Component-specific spacing
  component: {
    // Button spacing
    buttonPadding: {
      horizontal: 24,
      vertical: 12,
    },
    buttonMargin: 8,
    
    // Input spacing
    inputPadding: {
      horizontal: 16,
      vertical: 12,
    },
    inputMargin: 8,
    
    // Card spacing
    cardPadding: 16,
    cardMargin: 12,
    
    // List item spacing
    listItemPadding: {
      horizontal: 16,
      vertical: 12,
    },
    
    // Header spacing
    headerPadding: {
      horizontal: 16,
      vertical: 12,
    },
  },
  
  // Border radius values
  borderRadius: {
    none: 0,
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    '2xl': 20,
    full: 9999,
  },
  
  // Icon sizes
  iconSize: {
    xs: 16,
    sm: 20,
    md: 24,
    lg: 28,
    xl: 32,
    '2xl': 40,
    '3xl': 48,
  },
};

export type SpacingSize = keyof typeof Spacing;
export type BorderRadius = keyof typeof Spacing.borderRadius;
export type IconSize = keyof typeof Spacing.iconSize;
