import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  TextStyle,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from '../../contexts/ThemeContext';

interface HeaderProps {
  title: string;
  subtitle?: string;
  showBackButton?: boolean;
  onBackPress?: () => void;
  rightIcon?: string;
  onRightIconPress?: () => void;
  style?: ViewStyle;
  titleStyle?: TextStyle;
  backgroundColor?: string;
}

export const Header: React.FC<HeaderProps> = ({
  title,
  subtitle,
  showBackButton = false,
  onBackPress,
  rightIcon,
  onRightIconPress,
  style,
  titleStyle,
  backgroundColor,
}) => {
  const { theme } = useTheme();
  const insets = useSafeAreaInsets();

  const getContainerStyle = (): ViewStyle => ({
    ...theme.components.header.default,
    backgroundColor: backgroundColor || theme.colors.surface,
    paddingTop: insets.top,
    flexDirection: theme.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    minHeight: 56 + insets.top,
  });

  const getTitleContainerStyle = (): ViewStyle => ({
    flex: 1,
    alignItems: theme.isRTL ? 'flex-end' : 'flex-start',
    marginHorizontal: theme.spacing.md,
  });

  const getTitleStyle = (): TextStyle => ({
    ...theme.typography.textStyles.h3,
    color: theme.colors.text,
    textAlign: theme.isRTL ? 'right' : 'left',
  });

  const getSubtitleStyle = (): TextStyle => ({
    ...theme.typography.textStyles.bodySmall,
    color: theme.colors.textSecondary,
    textAlign: theme.isRTL ? 'right' : 'left',
    marginTop: 2,
  });

  const getIconButtonStyle = (): ViewStyle => ({
    padding: theme.spacing.sm,
    borderRadius: theme.spacing.borderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 40,
    minHeight: 40,
  });

  const renderIconButton = (iconName: string, onPress?: () => void) => (
    <TouchableOpacity
      style={getIconButtonStyle()}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <Ionicons
        name={iconName as any}
        size={theme.spacing.iconSize.md}
        color={theme.colors.text}
      />
    </TouchableOpacity>
  );

  return (
    <>
      <StatusBar
        backgroundColor={backgroundColor || theme.colors.surface}
        barStyle={theme.colors.text === '#FFFFFF' ? 'light-content' : 'dark-content'}
      />
      <View style={[getContainerStyle(), style]}>
        {showBackButton && renderIconButton(
          theme.isRTL ? 'chevron-forward' : 'chevron-back',
          onBackPress
        )}
        
        <View style={getTitleContainerStyle()}>
          <Text style={[getTitleStyle(), titleStyle]} numberOfLines={1}>
            {title}
          </Text>
          {subtitle && (
            <Text style={getSubtitleStyle()} numberOfLines={1}>
              {subtitle}
            </Text>
          )}
        </View>
        
        {rightIcon && renderIconButton(rightIcon, onRightIconPress)}
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  // Additional styles can be added here if needed
});
