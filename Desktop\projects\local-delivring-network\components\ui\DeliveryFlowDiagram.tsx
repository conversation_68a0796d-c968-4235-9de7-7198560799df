import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface FlowStep {
  id: string;
  icon: keyof typeof Ionicons.glyphMap;
  title: string;
  description: string;
  position: 'A' | 'B' | 'C' | 'D';
}

const flowSteps: FlowStep[] = [
  {
    id: '1',
    icon: 'person',
    title: 'أنت (المرسل)',
    description: 'في الموقع A',
    position: 'A'
  },
  {
    id: '2',
    icon: 'location',
    title: 'وجهة الطرد',
    description: 'الموقع C',
    position: 'C'
  },
  {
    id: '3',
    icon: 'car',
    title: 'سائق قريب',
    description: 'في الموقع C',
    position: 'C'
  },
  {
    id: '4',
    icon: 'car',
    title: 'سائق آخر',
    description: 'في الموقع D',
    position: 'D'
  }
];

export const DeliveryFlowDiagram: React.FC = () => {
  const { theme } = useTheme();

  const styles = StyleSheet.create({
    container: {
      padding: theme.spacing.lg,
      backgroundColor: theme.colors.surface,
      borderRadius: theme.spacing.borderRadius.lg,
      marginVertical: theme.spacing.md,
    },
    title: {
      ...theme.typography.textStyles.h4,
      color: theme.colors.text,
      textAlign: 'center',
      marginBottom: theme.spacing.lg,
      fontWeight: '600',
    },
    diagramContainer: {
      alignItems: 'center',
    },
    stepRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-around',
      width: '100%',
      marginBottom: theme.spacing.lg,
    },
    stepItem: {
      alignItems: 'center',
      flex: 1,
      paddingHorizontal: theme.spacing.xs,
    },
    stepIcon: {
      width: 50,
      height: 50,
      borderRadius: 25,
      backgroundColor: theme.colors.primaryLight + '30',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: theme.spacing.sm,
    },
    senderIcon: {
      backgroundColor: theme.colors.primary + '30',
    },
    destinationIcon: {
      backgroundColor: theme.colors.warning + '30',
    },
    driverIcon: {
      backgroundColor: theme.colors.success + '30',
    },
    stepTitle: {
      ...theme.typography.textStyles.caption,
      color: theme.colors.text,
      textAlign: 'center',
      fontWeight: '600',
      marginBottom: 2,
    },
    stepDescription: {
      ...theme.typography.textStyles.caption,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      fontSize: 10,
    },
    arrowContainer: {
      alignItems: 'center',
      marginVertical: theme.spacing.sm,
    },
    processSteps: {
      marginTop: theme.spacing.lg,
      paddingTop: theme.spacing.lg,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
    },
    processStep: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: theme.spacing.md,
      paddingHorizontal: theme.spacing.sm,
    },
    processNumber: {
      width: 24,
      height: 24,
      borderRadius: 12,
      backgroundColor: theme.colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: theme.spacing.sm,
      marginLeft: theme.spacing.sm,
    },
    processNumberText: {
      ...theme.typography.textStyles.caption,
      color: theme.colors.white,
      fontWeight: '600',
      fontSize: 12,
    },
    processText: {
      ...theme.typography.textStyles.bodySmall,
      color: theme.colors.text,
      flex: 1,
      textAlign: theme.isRTL ? 'right' : 'left',
    },
  });

  const getIconStyle = (position: string, id: string) => {
    if (id === '1') return [styles.stepIcon, styles.senderIcon];
    if (id === '2') return [styles.stepIcon, styles.destinationIcon];
    return [styles.stepIcon, styles.driverIcon];
  };

  const getIconColor = (position: string, id: string) => {
    if (id === '1') return theme.colors.primary;
    if (id === '2') return theme.colors.warning;
    return theme.colors.success;
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>كيف يعمل نظام التوصيل الذكي؟</Text>
      
      <View style={styles.diagramContainer}>
        {/* First Row - Sender and Destination */}
        <View style={styles.stepRow}>
          <View style={styles.stepItem}>
            <View style={getIconStyle('A', '1')}>
              <Ionicons
                name={flowSteps[0].icon}
                size={24}
                color={getIconColor('A', '1')}
              />
            </View>
            <Text style={styles.stepTitle}>{flowSteps[0].title}</Text>
            <Text style={styles.stepDescription}>{flowSteps[0].description}</Text>
          </View>
          
          <View style={styles.arrowContainer}>
            <Ionicons
              name="arrow-forward"
              size={20}
              color={theme.colors.textSecondary}
            />
          </View>
          
          <View style={styles.stepItem}>
            <View style={getIconStyle('C', '2')}>
              <Ionicons
                name={flowSteps[1].icon}
                size={24}
                color={getIconColor('C', '2')}
              />
            </View>
            <Text style={styles.stepTitle}>{flowSteps[1].title}</Text>
            <Text style={styles.stepDescription}>{flowSteps[1].description}</Text>
          </View>
        </View>

        {/* Arrow Down */}
        <View style={styles.arrowContainer}>
          <Ionicons
            name="arrow-down"
            size={20}
            color={theme.colors.textSecondary}
          />
          <Text style={[styles.stepDescription, { marginTop: 4 }]}>
            السائقون القريبون من الوجهة
          </Text>
        </View>

        {/* Second Row - Available Drivers */}
        <View style={styles.stepRow}>
          <View style={styles.stepItem}>
            <View style={getIconStyle('C', '3')}>
              <Ionicons
                name={flowSteps[2].icon}
                size={24}
                color={getIconColor('C', '3')}
              />
            </View>
            <Text style={styles.stepTitle}>{flowSteps[2].title}</Text>
            <Text style={styles.stepDescription}>{flowSteps[2].description}</Text>
          </View>
          
          <View style={styles.stepItem}>
            <View style={getIconStyle('D', '4')}>
              <Ionicons
                name={flowSteps[3].icon}
                size={24}
                color={getIconColor('D', '4')}
              />
            </View>
            <Text style={styles.stepTitle}>{flowSteps[3].title}</Text>
            <Text style={styles.stepDescription}>{flowSteps[3].description}</Text>
          </View>
        </View>
      </View>

      {/* Process Steps */}
      <View style={styles.processSteps}>
        <View style={styles.processStep}>
          <View style={styles.processNumber}>
            <Text style={styles.processNumberText}>1</Text>
          </View>
          <Text style={styles.processText}>
            تنشر طلب التوصيل من A إلى C
          </Text>
        </View>
        
        <View style={styles.processStep}>
          <View style={styles.processNumber}>
            <Text style={styles.processNumberText}>2</Text>
          </View>
          <Text style={styles.processText}>
            السائقون القريبون من C و D يرون طلبك
          </Text>
        </View>
        
        <View style={styles.processStep}>
          <View style={styles.processNumber}>
            <Text style={styles.processNumberText}>3</Text>
          </View>
          <Text style={styles.processText}>
            تختار أفضل عرض حسب السعر والتقييم
          </Text>
        </View>
        
        <View style={styles.processStep}>
          <View style={styles.processNumber}>
            <Text style={styles.processNumberText}>4</Text>
          </View>
          <Text style={styles.processText}>
            السائق يأتي إليك ويوصل الطرد للوجهة
          </Text>
        </View>
      </View>
    </View>
  );
};
