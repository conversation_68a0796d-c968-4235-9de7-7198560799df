import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useRef, useState } from 'react';
import {
  Dimensions,
  FlatList,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Button, DeliveryFlowDiagram } from '../components/ui';
import { useTheme } from '../contexts/ThemeContext';

const { width } = Dimensions.get('window');

interface OnboardingSlide {
  id: string;
  icon: keyof typeof Ionicons.glyphMap;
  title: string;
  description: string;
  features: string[];
}

const onboardingData: OnboardingSlide[] = [
  {
    id: '1',
    icon: 'chatbubbles',
    title: 'تواصل مباشر مع مقدمي الخدمات',
    description: 'تحدث مع السائقين ومقدمي خدمات التوصيل مباشرة',
    features: [
      'محادثة فورية مع السائقين',
      'تتبع حالة الطلب في الوقت الفعلي',
      'إرسال الموقع والتفاصيل بسهولة',
      'تأكيد الاستلام والتسليم'
    ]
  },
  {
    id: '2',
    icon: 'time',
    title: 'حدد أوقات العمل المتاحة',
    description: 'للسائقين: اختر الأوقات التي تريد العمل فيها',
    features: [
      'تحديد ساعات العمل اليومية',
      'إيقاف وتشغيل الخدمة حسب الحاجة',
      'إشعارات الطلبات في أوقات العمل فقط',
      'مرونة كاملة في إدارة الوقت'
    ]
  },
  {
    id: '3',
    icon: 'star',
    title: 'تقييمات ومراجعات المستخدمين',
    description: 'نظام تقييم شامل لضمان جودة الخدمة',
    features: [
      'تقييم السائقين والعملاء',
      'مراجعات مفصلة للخدمات',
      'نظام نقاط الثقة والموثوقية',
      'تحسين مستمر لجودة الخدمة'
    ]
  },
  {
    id: '4',
    icon: 'location',
    title: 'خريطة تفاعلية ومواقع قريبة',
    description: 'اعثر على أقرب السائقين والخدمات',
    features: [
      'عرض السائقين القريبين على الخريطة',
      'تتبع موقع الطلب في الوقت الفعلي',
      'حساب المسافة والوقت المتوقع',
      'اختيار أفضل مسار للتوصيل'
    ]
  },
  {
    id: '5',
    icon: 'flash',
    title: 'خيارات الشحن المتنوعة',
    description: 'اختر نوع الشحن المناسب لاحتياجاتك',
    features: [
      'شحن سريع: التسليم خلال 6 ساعات',
      'شحن مجدول: حدد التاريخ والوقت المناسب',
      'شحن اقتصادي: أسعار مخفضة مع وقت أطول',
      'شحن ليلي: خدمة 24 ساعة للطوارئ',
      'إمكانية تعديل أو إلغاء الطلب',
      'تنبيهات قبل الوصول بـ 30 دقيقة'
    ]
  },
  {
    id: '6',
    icon: 'car',
    title: 'شحن بين المدن المغربية',
    description: 'خدمة توصيل شاملة في جميع أنحاء المغرب',
    features: [
      'توصيل بين جميع المدن المغربية',
      'أسعار تنافسية للمسافات الطويلة',
      'تتبع الشحنات عبر المدن',
      'ضمان الأمان والسرعة'
    ]
  },
  {
    id: '6',
    icon: 'notifications',
    title: 'إشعارات فورية في الوقت الفعلي',
    description: 'ابق على اطلاع بكل تحديثات طلباتك',
    features: [
      'إشعار فوري عند قبول طلبك',
      'تحديثات موقع السائق أثناء الرحلة',
      'تنبيه عند وصول الطرد لوجهته',
      'إشعارات العروض والأسعار الجديدة',
      'تأكيد الاستلام والتسليم'
    ]
  },
  {
    id: '7',
    icon: 'navigate',
    title: 'كيف يعمل التطبيق؟',
    description: 'خطوات مفصلة لإرسال طرد مع خيارات التوقيت',
    features: [
      '1. حدد موقع الاستلام والتسليم',
      '2. اختر نوع الشحن: سريع (6 ساعات) أو مجدول',
      '3. للشحن المجدول: اختر التاريخ والوقت المناسب',
      '4. أضف تفاصيل الطرد والتعليمات الخاصة',
      '5. اعرض طلبك على السائقين المتاحين',
      '6. قارن العروض واختر الأنسب',
      '7. تتبع رحلة طردك لحظة بلحظة'
    ]
  },
  {
    id: '9',
    icon: 'time-outline',
    title: 'جدولة مرنة للتوصيل',
    description: 'تحكم كامل في أوقات الاستلام والتسليم',
    features: [
      '⚡ شحن سريع: خلال 6 ساعات من الطلب',
      '📅 شحن مجدول: حدد التاريخ والوقت بدقة',
      '💰 شحن اقتصادي: وفر المال مع وقت إضافي',
      '🔔 تنبيهات ذكية قبل الوصول بـ 30 دقيقة',
      '⏰ إمكانية تعديل المواعيد حتى آخر لحظة',
      '🌙 خدمة ليلية متاحة للطوارئ'
    ]
  }
];

export default function Onboarding() {
  const { theme } = useTheme();
  const [currentIndex, setCurrentIndex] = useState(0);
  const flatListRef = useRef<FlatList>(null);

  const handleNext = () => {
    if (currentIndex < onboardingData.length - 1) {
      const nextIndex = currentIndex + 1;
      setCurrentIndex(nextIndex);
      flatListRef.current?.scrollToIndex({ index: nextIndex, animated: true });
    } else {
      handleGetStarted();
    }
  };

  const handlePrevious = () => {
    if (currentIndex > 0) {
      const prevIndex = currentIndex - 1;
      setCurrentIndex(prevIndex);
      flatListRef.current?.scrollToIndex({ index: prevIndex, animated: true });
    }
  };

  const handleSkip = () => {
    router.push('/auth/register');
  };

  const handleGetStarted = () => {
    router.push('/auth/register');
  };

  const renderSlide = ({ item }: { item: OnboardingSlide }) => {
    // Slides with large content that need scrolling
    const needsScrolling = ['5', '8', '9'].includes(item.id);

    const slideContent = (
      <>
        {item.id !== '8' && (
          <View style={styles.iconContainer}>
            <Ionicons
              name={item.icon}
              size={80}
              color={theme.colors.primary}
            />
          </View>
        )}

        <Text style={[styles.title, { color: theme.colors.text }]}>
          {item.title}
        </Text>

        <Text style={[styles.description, { color: theme.colors.textSecondary }]}>
          {item.description}
        </Text>

        {item.id === '8' ? (
          <DeliveryFlowDiagram />
        ) : (
          <View style={styles.featuresContainer}>
            {item.features.map((feature, index) => (
              <View key={index} style={styles.featureItem}>
                <Ionicons
                  name="checkmark-circle"
                  size={20}
                  color={theme.colors.success}
                  style={styles.checkIcon}
                />
                <Text style={[styles.featureText, { color: theme.colors.text }]}>
                  {feature}
                </Text>
              </View>
            ))}
          </View>
        )}
      </>
    );

    return (
      <View style={[styles.slide, { width }]}>
        {needsScrolling ? (
          <>
            <ScrollView
              style={styles.scrollContainer}
              contentContainerStyle={styles.scrollContent}
              showsVerticalScrollIndicator={true}
              bounces={true}
              indicatorStyle={theme.colors.background === '#000000' ? 'white' : 'black'}
            >
              {slideContent}
            </ScrollView>
            <Text style={styles.scrollHint}>
              اسحب لأعلى وأسفل لرؤية المزيد
            </Text>
          </>
        ) : (
          slideContent
        )}
      </View>
    );
  };

  const renderPagination = () => (
    <View style={styles.paginationContainer}>
      <View style={styles.paginationWrapper}>
        {onboardingData.map((_, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.paginationDot,
              {
                backgroundColor: index === currentIndex
                  ? theme.colors.primary
                  : theme.colors.border,
                transform: [{ scale: index === currentIndex ? 1.2 : 1 }],
              },
            ]}
            onPress={() => {
              setCurrentIndex(index);
              flatListRef.current?.scrollToIndex({ index, animated: true });
            }}
          />
        ))}
      </View>
      <Text style={styles.paginationText}>
        {currentIndex + 1} من {onboardingData.length}
      </Text>
    </View>
  );

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    topContainer: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      paddingHorizontal: theme.spacing.screen.horizontal,
      paddingTop: theme.spacing.md,
      paddingBottom: theme.spacing.sm,
    },
    contentArea: {
      flex: 1,
    },
    slide: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: theme.spacing.screen.horizontal,
    },
    scrollContainer: {
      flex: 1,
      width: '100%',
    },
    scrollContent: {
      flexGrow: 1,
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: theme.spacing.lg,
    },
    iconContainer: {
      width: 120,
      height: 120,
      borderRadius: 60,
      backgroundColor: theme.colors.primaryLight + '20',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: theme.spacing['4xl'],
    },
    title: {
      ...theme.typography.textStyles.h2,
      textAlign: 'center',
      marginBottom: theme.spacing.lg,
      fontWeight: '700',
    },
    description: {
      ...theme.typography.textStyles.body,
      textAlign: 'center',
      marginBottom: theme.spacing['3xl'],
      lineHeight: 24,
    },
    featuresContainer: {
      width: '100%',
      maxWidth: 300,
    },
    featureItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: theme.spacing.md,
      paddingHorizontal: theme.spacing.sm,
    },
    checkIcon: {
      marginRight: theme.spacing.sm,
      marginLeft: theme.spacing.sm,
    },
    featureText: {
      ...theme.typography.textStyles.bodySmall,
      flex: 1,
      textAlign: theme.isRTL ? 'right' : 'left',
    },
    paginationContainer: {
      alignItems: 'center',
      paddingVertical: theme.spacing.lg,
    },
    paginationWrapper: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: theme.spacing.sm,
    },
    paginationDot: {
      width: 10,
      height: 10,
      borderRadius: 5,
      marginHorizontal: 5,
    },
    paginationText: {
      ...theme.typography.textStyles.caption,
      color: theme.colors.textSecondary,
      textAlign: 'center',
    },
    bottomContainer: {
      paddingHorizontal: theme.spacing.screen.horizontal,
      paddingBottom: theme.spacing.lg,
    },
    bottomNavigationContainer: {
      marginTop: theme.spacing.md,
    },
    swipeHint: {
      ...theme.typography.textStyles.caption,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      marginTop: theme.spacing.sm,
      fontStyle: 'italic',
    },
    skipButton: {
      paddingVertical: theme.spacing.sm,
      paddingHorizontal: theme.spacing.md,
      borderRadius: theme.spacing.borderRadius.md,
    },
    skipText: {
      ...theme.typography.textStyles.body,
      color: theme.colors.textSecondary,
      fontWeight: '500',
    },
    navigationButtons: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      gap: theme.spacing.md,
    },
    navButton: {
      flex: 1,
      paddingVertical: theme.spacing.md,
      paddingHorizontal: theme.spacing.lg,
      borderRadius: theme.spacing.borderRadius.md,
      backgroundColor: theme.colors.primary,
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: 48,
    },
    navButtonDisabled: {
      backgroundColor: theme.colors.border,
    },
    navButtonText: {
      ...theme.typography.textStyles.body,
      color: theme.colors.textOnPrimary,
      fontWeight: '600',
    },
    navButtonTextDisabled: {
      color: theme.colors.textSecondary,
    },
    getStartedButton: {
      marginTop: theme.spacing.sm,
    },
    scrollHint: {
      ...theme.typography.textStyles.caption,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      marginTop: theme.spacing.xs,
      fontStyle: 'italic',
      fontSize: 12,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      {/* Skip button at top right */}
      <View style={styles.topContainer}>
        <TouchableOpacity style={styles.skipButton} onPress={handleSkip}>
          <Text style={styles.skipText}>تخطي</Text>
        </TouchableOpacity>
      </View>

      {/* Main content area */}
      <View style={styles.contentArea}>
        <FlatList
          ref={flatListRef}
          data={onboardingData}
          renderItem={renderSlide}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onMomentumScrollEnd={(event) => {
            const index = Math.round(event.nativeEvent.contentOffset.x / width);
            setCurrentIndex(index);
          }}
          scrollEnabled={true}
          decelerationRate="fast"
          snapToInterval={width}
          snapToAlignment="start"
          bounces={false}
        />
      </View>

      {/* Bottom navigation area */}
      <View style={styles.bottomContainer}>
        {renderPagination()}

        {/* Swipe hint */}
        <Text style={styles.swipeHint}>اسحب يميناً أو يساراً للتنقل</Text>

        <View style={styles.bottomNavigationContainer}>
          {currentIndex === onboardingData.length - 1 ? (
            <Button
              title="ابدأ الآن"
              onPress={handleGetStarted}
              style={styles.getStartedButton}
              fullWidth
            />
          ) : (
            <View style={styles.navigationButtons}>
              <TouchableOpacity
                style={[styles.navButton, currentIndex === 0 && styles.navButtonDisabled]}
                onPress={handlePrevious}
                disabled={currentIndex === 0}
              >
                <Text style={[styles.navButtonText, currentIndex === 0 && styles.navButtonTextDisabled]}>
                  السابق
                </Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.navButton} onPress={handleNext}>
                <Text style={styles.navButtonText}>التالي</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
    </SafeAreaView>
  );
}
