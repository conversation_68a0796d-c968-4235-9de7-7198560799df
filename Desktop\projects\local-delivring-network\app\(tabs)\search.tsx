import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Card, Header, TextInput } from '../../components/ui';
import { Strings } from '../../constants';
import { useTheme } from '../../contexts/ThemeContext';

export default function Search() {
  const { theme } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  const categories = [
    { id: 1, name: 'توصيل الطعام', icon: 'restaurant', color: theme.colors.primary },
    { id: 2, name: 'البقالة', icon: 'storefront', color: theme.colors.secondary },
    { id: 3, name: 'الأدوية', icon: 'medical', color: theme.colors.accent },
    { id: 4, name: 'الملابس', icon: 'shirt', color: theme.colors.info },
    { id: 5, name: 'الإلكترونيات', icon: 'phone-portrait', color: theme.colors.warning },
    { id: 6, name: 'أخرى', icon: 'ellipsis-horizontal', color: theme.colors.textSecondary },
  ];

  const recentSearches = [
    'مطعم البرجر',
    'صيدلية الشفاء',
    'سوق الخضار',
    'محل الملابس',
  ];

  const popularServices = [
    {
      id: 1,
      name: 'مطعم الأصالة',
      category: 'توصيل الطعام',
      rating: 4.8,
      deliveryTime: '30-45 دقيقة',
      deliveryFee: '15 درهم',
      image: null,
    },
    {
      id: 2,
      name: 'صيدلية النور',
      category: 'الأدوية',
      rating: 4.9,
      deliveryTime: '15-30 دقيقة',
      deliveryFee: '10 درهم',
      image: null,
    },
    {
      id: 3,
      name: 'سوق الخير',
      category: 'البقالة',
      rating: 4.6,
      deliveryTime: '45-60 دقيقة',
      deliveryFee: '20 درهم',
      image: null,
    },
  ];

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    searchContainer: {
      padding: theme.spacing.screen.horizontal,
      backgroundColor: theme.colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    scrollContainer: {
      padding: theme.spacing.screen.horizontal,
      paddingBottom: theme.spacing['4xl'],
    },
    sectionContainer: {
      marginBottom: theme.spacing['2xl'],
    },
    sectionTitle: {
      ...theme.typography.textStyles.h3,
      color: theme.colors.text,
      marginBottom: theme.spacing.lg,
      textAlign: theme.isRTL ? 'right' : 'left',
    },
    categoriesGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
      gap: theme.spacing.md,
    },
    categoryItem: {
      width: '48%',
      alignItems: 'center',
      padding: theme.spacing.lg,
      borderRadius: theme.spacing.borderRadius.lg,
      backgroundColor: theme.colors.surface,
      borderWidth: 2,
      borderColor: 'transparent',
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    selectedCategory: {
      borderColor: theme.colors.primary,
      backgroundColor: theme.colors.primaryLight + '10',
    },
    categoryIcon: {
      marginBottom: theme.spacing.sm,
    },
    categoryText: {
      ...theme.typography.textStyles.bodySmall,
      color: theme.colors.text,
      textAlign: 'center',
      fontWeight: '500',
    },
    recentSearchesContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: theme.spacing.sm,
    },
    recentSearchItem: {
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: theme.spacing.borderRadius.full,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    recentSearchText: {
      ...theme.typography.textStyles.bodySmall,
      color: theme.colors.textSecondary,
    },
    serviceCard: {
      marginBottom: theme.spacing.md,
      padding: theme.spacing.lg,
    },
    serviceHeader: {
      flexDirection: theme.isRTL ? 'row-reverse' : 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: theme.spacing.sm,
    },
    serviceInfo: {
      flex: 1,
    },
    serviceName: {
      ...theme.typography.textStyles.body,
      color: theme.colors.text,
      fontWeight: '600',
      marginBottom: theme.spacing.xs,
      textAlign: theme.isRTL ? 'right' : 'left',
    },
    serviceCategory: {
      ...theme.typography.textStyles.bodySmall,
      color: theme.colors.textSecondary,
      marginBottom: theme.spacing.sm,
      textAlign: theme.isRTL ? 'right' : 'left',
    },
    serviceDetails: {
      flexDirection: theme.isRTL ? 'row-reverse' : 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    serviceRating: {
      flexDirection: theme.isRTL ? 'row-reverse' : 'row',
      alignItems: 'center',
      gap: theme.spacing.xs,
    },
    ratingText: {
      ...theme.typography.textStyles.bodySmall,
      color: theme.colors.accent,
      fontWeight: '600',
    },
    deliveryInfo: {
      alignItems: theme.isRTL ? 'flex-start' : 'flex-end',
    },
    deliveryTime: {
      ...theme.typography.textStyles.bodySmall,
      color: theme.colors.text,
      fontWeight: '500',
    },
    deliveryFee: {
      ...theme.typography.textStyles.bodySmall,
      color: theme.colors.primary,
      fontWeight: '600',
    },
    emptyState: {
      alignItems: 'center',
      padding: theme.spacing['4xl'],
    },
    emptyStateIcon: {
      marginBottom: theme.spacing.lg,
    },
    emptyStateTitle: {
      ...theme.typography.textStyles.h3,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      marginBottom: theme.spacing.sm,
    },
    emptyStateText: {
      ...theme.typography.textStyles.body,
      color: theme.colors.textSecondary,
      textAlign: 'center',
    },
    filterButton: {
      flexDirection: theme.isRTL ? 'row-reverse' : 'row',
      alignItems: 'center',
      gap: theme.spacing.sm,
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.sm,
      backgroundColor: theme.colors.surface,
      borderRadius: theme.spacing.borderRadius.full,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    filterButtonText: {
      ...theme.typography.textStyles.bodySmall,
      color: theme.colors.text,
      fontWeight: '500',
    },
  });

  const renderCategory = (category: typeof categories[0]) => (
    <TouchableOpacity
      key={category.id}
      style={[
        styles.categoryItem,
        selectedCategory === category.name && styles.selectedCategory,
      ]}
      onPress={() => setSelectedCategory(
        selectedCategory === category.name ? null : category.name
      )}
      activeOpacity={0.8}
    >
      <Ionicons
        name={category.icon as any}
        size={theme.spacing.iconSize['2xl']}
        color={category.color}
        style={styles.categoryIcon}
      />
      <Text style={styles.categoryText}>{category.name}</Text>
    </TouchableOpacity>
  );

  const renderRecentSearch = (search: string, index: number) => (
    <TouchableOpacity
      key={index}
      style={styles.recentSearchItem}
      onPress={() => setSearchQuery(search)}
      activeOpacity={0.8}
    >
      <Text style={styles.recentSearchText}>{search}</Text>
    </TouchableOpacity>
  );

  const renderServiceCard = (service: typeof popularServices[0]) => (
    <Card key={service.id} style={styles.serviceCard}>
      <View style={styles.serviceHeader}>
        <View style={styles.serviceInfo}>
          <Text style={styles.serviceName}>{service.name}</Text>
          <Text style={styles.serviceCategory}>{service.category}</Text>
        </View>
      </View>
      <View style={styles.serviceDetails}>
        <View style={styles.serviceRating}>
          <Ionicons
            name="star"
            size={theme.spacing.iconSize.sm}
            color={theme.colors.accent}
          />
          <Text style={styles.ratingText}>{service.rating}</Text>
        </View>
        <View style={styles.deliveryInfo}>
          <Text style={styles.deliveryTime}>{service.deliveryTime}</Text>
          <Text style={styles.deliveryFee}>{service.deliveryFee}</Text>
        </View>
      </View>
    </Card>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons
        name="search"
        size={theme.spacing.iconSize['3xl']}
        color={theme.colors.textTertiary}
        style={styles.emptyStateIcon}
      />
      <Text style={styles.emptyStateTitle}>{Strings.search.noResults}</Text>
      <Text style={styles.emptyStateText}>
        {Strings.search.tryDifferentKeywords}
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title={Strings.search.title}
        rightIcon="options"
        onRightIconPress={() => console.log('Filters')}
      />
      
      <View style={styles.searchContainer}>
        <TextInput
          placeholder={Strings.search.placeholder}
          value={searchQuery}
          onChangeText={setSearchQuery}
          leftIcon="search"
          rightIcon={searchQuery ? 'close' : undefined}
          onRightIconPress={() => setSearchQuery('')}
        />
      </View>
      
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>{Strings.search.categories}</Text>
          <View style={styles.categoriesGrid}>
            {categories.map(renderCategory)}
          </View>
        </View>

        {!searchQuery && (
          <>
            <View style={styles.sectionContainer}>
              <Text style={styles.sectionTitle}>
                {Strings.search.recentSearches}
              </Text>
              <View style={styles.recentSearchesContainer}>
                {recentSearches.map(renderRecentSearch)}
              </View>
            </View>

            <View style={styles.sectionContainer}>
              <Text style={styles.sectionTitle}>
                {Strings.search.popularServices}
              </Text>
              {popularServices.map(renderServiceCard)}
            </View>
          </>
        )}

        {searchQuery && popularServices.length === 0 && renderEmptyState()}
        {searchQuery && popularServices.length > 0 && (
          <View style={styles.sectionContainer}>
            <Text style={styles.sectionTitle}>نتائج البحث</Text>
            {popularServices.map(renderServiceCard)}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}
