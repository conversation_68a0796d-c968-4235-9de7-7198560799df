import { Colors, ColorScheme } from './Colors';
import { Typography } from './Typography';
import { Spacing } from './Spacing';

// Main theme configuration
export const createTheme = (colorScheme: ColorScheme) => ({
  colors: Colors[colorScheme],
  typography: Typography,
  spacing: Spacing,
  
  // Common component styles
  components: {
    // Button styles
    button: {
      primary: {
        backgroundColor: Colors[colorScheme].primary,
        color: Colors[colorScheme].textOnPrimary,
        borderRadius: Spacing.borderRadius.md,
        paddingHorizontal: Spacing.component.buttonPadding.horizontal,
        paddingVertical: Spacing.component.buttonPadding.vertical,
        ...Typography.textStyles.button,
      },
      secondary: {
        backgroundColor: Colors[colorScheme].secondary,
        color: Colors[colorScheme].textOnSecondary,
        borderRadius: Spacing.borderRadius.md,
        paddingHorizontal: Spacing.component.buttonPadding.horizontal,
        paddingVertical: Spacing.component.buttonPadding.vertical,
        ...Typography.textStyles.button,
      },
      outline: {
        backgroundColor: 'transparent',
        color: Colors[colorScheme].primary,
        borderColor: Colors[colorScheme].primary,
        borderWidth: 1,
        borderRadius: Spacing.borderRadius.md,
        paddingHorizontal: Spacing.component.buttonPadding.horizontal,
        paddingVertical: Spacing.component.buttonPadding.vertical,
        ...Typography.textStyles.button,
      },
      ghost: {
        backgroundColor: 'transparent',
        color: Colors[colorScheme].primary,
        borderRadius: Spacing.borderRadius.md,
        paddingHorizontal: Spacing.component.buttonPadding.horizontal,
        paddingVertical: Spacing.component.buttonPadding.vertical,
        ...Typography.textStyles.button,
      },
    },
    
    // Input styles
    input: {
      default: {
        backgroundColor: Colors[colorScheme].surface,
        color: Colors[colorScheme].text,
        borderColor: Colors[colorScheme].border,
        borderWidth: 1,
        borderRadius: Spacing.borderRadius.md,
        paddingHorizontal: Spacing.component.inputPadding.horizontal,
        paddingVertical: Spacing.component.inputPadding.vertical,
        ...Typography.textStyles.input,
      },
      focused: {
        borderColor: Colors[colorScheme].primary,
        borderWidth: 2,
      },
      error: {
        borderColor: Colors[colorScheme].error,
        borderWidth: 1,
      },
    },
    
    // Card styles
    card: {
      default: {
        backgroundColor: Colors[colorScheme].surface,
        borderRadius: Spacing.borderRadius.lg,
        padding: Spacing.component.cardPadding,
        shadowColor: Colors[colorScheme].shadow,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 2,
      },
      elevated: {
        backgroundColor: Colors[colorScheme].surface,
        borderRadius: Spacing.borderRadius.lg,
        padding: Spacing.component.cardPadding,
        shadowColor: Colors[colorScheme].shadowDark,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 8,
        elevation: 4,
      },
    },
    
    // Header styles
    header: {
      default: {
        backgroundColor: Colors[colorScheme].surface,
        borderBottomColor: Colors[colorScheme].border,
        borderBottomWidth: 1,
        paddingHorizontal: Spacing.component.headerPadding.horizontal,
        paddingVertical: Spacing.component.headerPadding.vertical,
      },
    },
  },
  
  // RTL support
  isRTL: true, // Set to true for Arabic content
  
  // Animation durations
  animation: {
    fast: 150,
    normal: 300,
    slow: 500,
  },
});

export type Theme = ReturnType<typeof createTheme>;

// Default themes
export const lightTheme = createTheme('light');
export const darkTheme = createTheme('dark');
