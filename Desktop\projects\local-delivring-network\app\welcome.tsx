import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React from 'react';
import {
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Button } from '../components/ui';
import { Strings } from '../constants';
import { useTheme } from '../contexts/ThemeContext';

export default function Welcome() {
  const { theme } = useTheme();

  const handleGetStarted = () => {
    router.push('/onboarding');
  };

  const handleLogin = () => {
    router.push('/auth/login');
  };

  const handleTermsPress = () => {
    // TODO: Navigate to terms and conditions screen
    console.log('Terms and Conditions pressed');
  };

  const handlePrivacyPress = () => {
    // TODO: Navigate to privacy policy screen
    console.log('Privacy Policy pressed');
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollContainer: {
      flexGrow: 1,
      padding: theme.spacing.screen.horizontal,
      justifyContent: 'space-between',
    },
    contentContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    logoContainer: {
      alignItems: 'center',
      marginBottom: theme.spacing['6xl'],
    },
    logoIcon: {
      width: 100,
      height: 100,
      borderRadius: 50,
      backgroundColor: theme.colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: theme.spacing['2xl'],
      shadowColor: theme.colors.shadow,
      shadowOffset: {
        width: 0,
        height: 4,
      },
      shadowOpacity: 0.15,
      shadowRadius: 8,
      elevation: 8,
    },
    title: {
      ...theme.typography.textStyles.h1,
      color: theme.colors.text,
      textAlign: 'center',
      marginBottom: theme.spacing.md,
      fontWeight: '700',
    },
    subtitle: {
      ...theme.typography.textStyles.body,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      lineHeight: 24,
      paddingHorizontal: theme.spacing.lg,
    },
    buttonContainer: {
      width: '100%',
      gap: theme.spacing.lg,
      marginTop: theme.spacing['4xl'],
    },
    primaryButton: {
      marginBottom: theme.spacing.md,
    },
    secondaryButton: {
      backgroundColor: 'transparent',
      borderWidth: 2,
      borderColor: theme.colors.primary,
    },
    secondaryButtonText: {
      color: theme.colors.primary,
    },
    footerContainer: {
      alignItems: 'center',
      paddingTop: theme.spacing['2xl'],
      paddingBottom: theme.spacing.lg,
    },
    termsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'center',
      alignItems: 'center',
      gap: theme.spacing.xs,
    },
    termsText: {
      ...theme.typography.textStyles.caption,
      color: theme.colors.textSecondary,
      textAlign: 'center',
    },
    termsLink: {
      ...theme.typography.textStyles.caption,
      color: theme.colors.primary,
      fontWeight: '600',
      textDecorationLine: 'underline',
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.contentContainer}>
          <View style={styles.logoContainer}>
            <View style={styles.logoIcon}>
              <Ionicons
                name="bus"
                size={50}
                color="#fff"
              />
            </View>
            <Text style={styles.title}>{Strings.appName}</Text>
            <Text style={styles.subtitle}>{Strings.appTagline}</Text>
          </View>

          <View style={styles.buttonContainer}>
            <Button
              title={Strings.welcome.getStarted}
              onPress={handleGetStarted}
              style={styles.primaryButton}
              fullWidth
            />

            <Button
              title={Strings.welcome.login}
              onPress={handleLogin}
              style={styles.secondaryButton}
              textStyle={styles.secondaryButtonText}
              fullWidth
            />
          </View>
        </View>

        <View style={styles.footerContainer}>
          <View style={styles.termsContainer}>
            <Text style={styles.termsText}>{Strings.welcome.agreeToTerms}</Text>
            <TouchableOpacity onPress={handleTermsPress}>
              <Text style={styles.termsLink}>{Strings.welcome.termsAndConditions}</Text>
            </TouchableOpacity>
            <Text style={styles.termsText}>و</Text>
            <TouchableOpacity onPress={handlePrivacyPress}>
              <Text style={styles.termsLink}>{Strings.welcome.privacyPolicy}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
